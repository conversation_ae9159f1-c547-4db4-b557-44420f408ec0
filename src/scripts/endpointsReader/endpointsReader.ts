/* eslint-disable no-console */
import { ENDPOINTS as billing } from 'services/api/oldApi/billing/endpoints';
import { ENDPOINTS as live } from 'services/api/newApi/live/channels/endpoints';
import { ENDPOINTS as myzone } from 'services/api/oldApi/myzone/endpoints';
import { ENDPOINTS as search } from 'services/api/oldApi/search/endpoints';
import { ENDPOINTS as system } from 'services/api/oldApi/system/endpoints';
import { ENDPOINTS as vod } from 'services/api/oldApi/vod/endpoints';
import { ENDPOINTS as appConfigs } from 'services/api/newApi/core/appConfigs/endpoints';
import { ENDPOINTS as banners } from 'services/api/newApi/optional/banners/endpoints';
import { ENDPOINTS as npvr } from 'services/api/newApi/optional/npvr/enpoints';

const namespaces = [
  billing,
  live,
  myzone,
  npvr,
  search,
  system,
  vod,
  appConfigs,
  banners,
];

for (const namespace of namespaces) {
  console.table(namespace);
}
