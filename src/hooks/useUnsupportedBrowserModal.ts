import { useEffect, useState } from 'react';

import { checkIsUnsupportedBrowser } from 'services/device';
import { useAuthenticationStatus } from 'services/user';

export const useUnsupportedBrowserModal = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { isAuthenticated } = useAuthenticationStatus();
  const isUnsupportedBrowser = checkIsUnsupportedBrowser();

  useEffect(() => {
    // Show modal only if user is authenticated and using unsupported browser
    if (isAuthenticated && isUnsupportedBrowser) {
      setIsModalOpen(true);
    } else {
      setIsModalOpen(false);
    }
  }, [isAuthenticated, isUnsupportedBrowser]);

  return {
    isModalOpen,
    isUnsupportedBrowser,
  };
};
