import { Dispatch, SetStateAction } from 'react';

import { Channel } from 'services/api/newApi/live/channels';
import { SearchResult } from 'services/api/oldApi/search';

export type SearchedChannel = SearchResult & Channel;
export interface CombinedSearchResult {
  lives: SearchResult[];
  vods: SearchResult[];
  actors: SearchResult[];
  channels: SearchedChannel[];
}
export enum SearchOrder {
  Default = 'defaultOrder',
  ChannelsOrder = 'channelsOrder',
  ProgramsOrder = 'programsOrder',
  VodsOrder = 'vodsOrder',
}
export interface SearchOrderContextValue {
  searchOrder: SearchOrder;
  locationOrder: SearchOrder;
  orderFromPlayer: SearchOrder;
  setOrderFromPlayer: Dispatch<SetStateAction<SearchOrder>>;
  handleSearchOrderWithPlayer: () => void;
}
