import { FC, PropsWithChildren, useMemo } from 'react';
import { getYear } from 'date-fns';

import { CheckboxControl } from 'components/Forms/CheckboxControl';
import { Section } from 'components/Section';
import { Link, Text } from 'components/Typography';
import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { Tooltip } from 'components/Tooltip';
import { AtvAppPopup } from 'components/AtvAppPopup';
import { PRIVACY_POLICY_EXTERNAL_LINK } from 'services/config/config';

import { messages } from './messages';
import { useService } from './hooks';
import * as S from './styles';

import * as Settings from '../styles';
import packageJson from '../../../../package.json';

export const Service: FC<PropsWithChildren> = () => {
  const {
    formatMessage,
    householdExtId,
    householdTve,
    handleCheckboxAnalyticsAgreement,
    getServiceBeginDate,
    analyticsAgreement,
    getServiceEndDate,
    isMultiRecorderActive,
    isTveSubscriptionInfoVisible,
    shouldShowWatchOnBigScreenButton,
    openAtvAppPopup,
    closeAtvAppPopup,
    isAtvAppPopupVisible,
  } = useService();

  const isTVEActive = useMemo(() => {
    return Boolean(householdTve && !householdTve.activeTo);
  }, [householdTve]);

  return (
    <Section title={formatMessage(messages.title)}>
      <>
        {isAtvAppPopupVisible && (
          <AtvAppPopup
            isOpen={isAtvAppPopupVisible}
            onClose={closeAtvAppPopup}
          />
        )}
        {isTveSubscriptionInfoVisible && (
          <Settings.InfoTextWrapper>
            <Text>
              {householdTve
                ? formatMessage(messages.subscriptionInfoText, {
                    serviceBeginDate: getServiceBeginDate(),
                  })
                : formatMessage(messages.infoTextError)}
              {!isTVEActive && (
                <Text>
                  {formatMessage(messages.infoTextUnsubscribeDate, {
                    serviceEndDate: getServiceEndDate(),
                  })}
                </Text>
              )}
            </Text>
          </Settings.InfoTextWrapper>
        )}
      </>
      <S.Grid>
        <Text>{formatMessage(messages.serviceId)}</Text>
        <Text>{householdExtId}</Text>
        {isMultiRecorderActive && (
          <>
            <Text>{formatMessage(messages.multiRecorder)}</Text>
            <S.ActionsWrapper>
              <Text $highlight>
                {formatMessage(messages.multiRecorderActive)}
              </Text>
            </S.ActionsWrapper>
          </>
        )}
        <Text>{formatMessage(messages.statisticData)}</Text>
        <S.ActionsWrapper>
          <S.CheckboxWrapper>
            <CheckboxControl
              id='statistic-data-accepted'
              label={formatMessage(messages.statisticAgreement)}
              isChecked={Boolean(analyticsAgreement)}
              onCheck={handleCheckboxAnalyticsAgreement}
              onClick={handleCheckboxAnalyticsAgreement}
            />
          </S.CheckboxWrapper>
          <Tooltip
            width={400}
            content={formatMessage(messages.analyticsAgreementInfo)}
          />
        </S.ActionsWrapper>
      </S.Grid>
      <S.OutsideGridElementWrapper>
        <Link href={PRIVACY_POLICY_EXTERNAL_LINK} target='_blank'>
          {formatMessage(messages.privacyPolicyLink)}
        </Link>
      </S.OutsideGridElementWrapper>
      {shouldShowWatchOnBigScreenButton ? (
        <S.OutsideGridElementWrapper>
          <PrimaryButton variant='orange' onClick={openAtvAppPopup}>
            <Text>{formatMessage(messages.watchOnBigScreenButton)}</Text>
          </PrimaryButton>
        </S.OutsideGridElementWrapper>
      ) : (
        <></>
      )}
      <S.Version>
        {formatMessage(messages.copyright)} {getYear(new Date())}
        {formatMessage(messages.appVersion, {
          appVersion: packageJson.version,
        })}
      </S.Version>
    </Section>
  );
};
