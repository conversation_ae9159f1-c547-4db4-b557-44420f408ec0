import { useIntl } from 'react-intl';

import { Section } from 'components/Section';
import { Text } from 'components/Typography';

import { messages } from './messages';
import * as S from './styles';

import * as Settings from '../styles';

export const Accessibility = () => {
  const { formatMessage } = useIntl();

  const renderAccessibilityList = () => {
    return Object.entries(messages.accessibilityList).map(([_key, value]) => {
      return (
        <li key={value.id}>
          <Text $bold>{formatMessage(value.prefix)}</Text>
          <Text>{formatMessage(value)}</Text>
        </li>
      );
    });
  };

  return (
    <Section title={formatMessage(messages.title)}>
      <Settings.InfoTextWrapper>
        <Text>{formatMessage(messages.description)}</Text>
        <S.ListWrapper>{renderAccessibilityList()}</S.ListWrapper>
      </Settings.InfoTextWrapper>
    </Section>
  );
};
