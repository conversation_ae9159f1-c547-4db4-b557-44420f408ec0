export const messages = {
  title: { id: 'accessibility.title', defaultMessage: 'Ułatwienia dostępu' },
  description: {
    id: 'accessibility.description',
    defaultMessage:
      'Nadawcy mogą przekazywać nam razem z kanałem telewizyjnym  audiodeskrypcję, napisy dla niesłyszących lub inne udogodnienia dla osób z niepełnosprawnościami. Będą Państwo mogli z nich skorzystać poprzez  nasz dekoder i naszą aplikację telewizyjną. Informację o oferowanych  udogodnieniach, a także elektroniczny przewodnik po programach (EPG),  będziemy prezentować w dekoderze i aplikacji zgodnie z wymaganiami  dostępności. Symbole, które zobaczysz, oznaczają:',
  },
  accessibilityList: {
    audio: {
      id: 'accessibility.audio',
      defaultMessage: '\xA0– audiodeskrypcja,',
      prefix: { id: 'accessibility.audio.prefix', defaultMessage: 'AD' },
    },
    subtitles: {
      id: 'accessibility.subtitles',
      defaultMessage: '\xA0– napisy dla niesłyszących,',
      prefix: { id: 'accessibility.subtitles.prefix', defaultMessage: 'N' },
    },
    translations: {
      id: 'accessibility.translations',
      defaultMessage: `\xA0– tłumaczenie na język migowy.`,
      prefix: {
        id: 'accessibility.translations.prefix',
        defaultMessage: 'JM',
      },
    },
  },
};
