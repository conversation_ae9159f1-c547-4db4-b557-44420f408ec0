import { useCallback, useEffect, useRef } from 'react';

import { useFullScreenChangeListener } from 'hooks/useFullScreenChangeListener';
import { useAppLayoutMode } from 'services/appLayoutMode';
import { globalConfig } from 'services/config/config';

import { usePlayerInstance } from '../BasePlayer/Context';
import { PlayerMode } from '../types';
import { usePlayerErrorScreen } from '../Context';

export const usePlayerControls = () => {
  const { player } = usePlayerInstance();

  const {
    mode,
    previous,
    isSearchOpen,
    setMode,
    setArePlayerControlsActive,
    arePlayerControlsVisible,
    setArePlayerControlsVisible,
    isBigMode,
  } = useAppLayoutMode();

  const { playerControlsTimeHidden } = globalConfig.player;

  const mount = useRef<boolean>(false);

  const { errorMessage } = usePlayerErrorScreen();

  const timer = useRef<any>(null);

  const handleMouseEnter = useCallback(() => {
    if (mode === PlayerMode.Mini) setArePlayerControlsVisible(true);
  }, [mode, setArePlayerControlsVisible]);

  const handleMouseLeave = useCallback(() => {
    if (mode === PlayerMode.Mini) setArePlayerControlsVisible(false);
  }, [mode, setArePlayerControlsVisible]);

  const handleClick = useCallback(() => {
    if (isBigMode && mount.current) {
      setArePlayerControlsVisible(false);
    }
  }, [isBigMode, setArePlayerControlsVisible]);

  const handleInterfaceClick = useCallback(
    (event: React.MouseEvent<HTMLDivElement, MouseEvent>): void => {
      // This will stop any parent component’s event from firing.
      event.stopPropagation();
    },
    [],
  );

  const setTimer = useCallback(() => {
    timer.current = setTimeout(() => {
      if (mount.current && isBigMode) {
        setArePlayerControlsVisible(false);
      }
    }, playerControlsTimeHidden * 1000);
  }, [isBigMode, playerControlsTimeHidden, setArePlayerControlsVisible]);

  const cancelTimer = useCallback(() => {
    if (isBigMode) {
      clearTimeout(timer.current);
      mount.current && setArePlayerControlsVisible(true);
      setTimer();
    }
  }, [isBigMode, setArePlayerControlsVisible, setTimer]);

  useFullScreenChangeListener((isFullScreen) => {
    setMode(isFullScreen ? PlayerMode.FullScreen : previous);

    // update player video area to  correctly render subtitles
    setTimeout(() => player?.updateVideoArea(), 200);
  });

  useEffect(() => {
    mount.current = true;

    return () => {
      mount.current = false;
    };
  }, []);

  useEffect(() => {
    if (isBigMode) setTimer();
    return () => {
      clearTimeout(timer.current);
    };
  }, [playerControlsTimeHidden, isBigMode, setTimer]);

  useEffect(() => {
    if (isSearchOpen) {
      mount.current && setArePlayerControlsVisible(false);
      return;
    }
    mount.current && setArePlayerControlsVisible(true);
  }, [isSearchOpen, mode, setArePlayerControlsVisible]);

  useEffect(() => {
    clearTimeout(timer.current);
  }, [mode]);

  useEffect(() => {
    errorMessage.message
      ? setArePlayerControlsActive(true)
      : setArePlayerControlsActive(arePlayerControlsVisible);
  }, [errorMessage, arePlayerControlsVisible, setArePlayerControlsActive]);

  return {
    arePlayerControlsVisible,
    handleMouseEnter,
    handleMouseLeave,
    cancelTimer,
    handleClick,
    handleInterfaceClick,
    setArePlayerControlsVisible,
  };
};
