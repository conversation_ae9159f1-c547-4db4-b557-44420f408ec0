import {
  FC,
  PropsWithChildren,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  BufferSensitivity,
  createPlayer,
  Player,
  PlayerConfiguration,
} from 'voplayer-html5';

import {
  PlayerInstanceProvider,
  PlayerStateProvider,
  PlayerTimeLimitProvider,
  PlayerTimerProvider,
} from 'features/Player/BasePlayer/Context';
import {
  LanguagesAutoTrackOptions,
  usePlayerErrorScreen,
} from 'features/Player/Context';
import { usePlayerInitialLanguages } from 'features/Player/Context/PlayerInitialLanguagesContext';
import { TimeInSeconds } from 'services/api/common/types';
import { useAppLayoutMode } from 'services/appLayoutMode';
import { useConfig } from 'services/config';
import { useGlobalLoaderContext } from 'services/loader';
import { useLogger } from 'services/logger';
import { checkIsUnsupportedBrowser } from 'services/device';

import * as S from './styles';
import { BasePlayerProps } from './types';
import { usePlayerError } from './Hook';
import { PlayerKeyActionsProvider } from './Context/PlayerKeyActionsContext';

import { PlayerOverlayContent, usePlayerOverlay } from '../PlayerOverlay';
import { PlayerSettingsProvider } from '../Interface/SettingsMenu/PlayerSettingsContext';

export const BasePlayer: FC<PropsWithChildren<BasePlayerProps>> = (props) => {
  const {
    mediaUrl,
    casToken,
    children,
    startPosition = 0,
    loop = false,
    isLiveStream = false,
    isVod = false,
    refetchToken,
  } = props;

  const containerRef = useRef<HTMLDivElement | null>(null);
  const [player, setPlayer] = useState<Player | null>(null);

  const { logger } = useLogger();
  const { config } = useConfig();
  const { mode } = useAppLayoutMode();
  const isUnsupportedBrowser = checkIsUnsupportedBrowser();
  const { errorMessage: playerErrorMessage } = usePlayerErrorScreen();
  const { setPlayerOverlay } = usePlayerOverlay();
  const { errorHandler } = usePlayerError();
  const { setIsLoaderVisible } = useGlobalLoaderContext();
  const { getInitialAutoTrack } = usePlayerInitialLanguages();

  const playerConfiguration = useMemo((): PlayerConfiguration => {
    const {
      appConfig: {
        player: { widevineDrmUrl },
        bitrate: { maxBitrate, maxBitrateVod, sensitivity },
      },
    } = config;

    const widevineUrl = casToken
      ? `${widevineDrmUrl}?token=${encodeURIComponent(casToken)}`
      : `${widevineDrmUrl}}`;

    const bufferSensitivity = (
      isLiveStream ? sensitivity.live : sensitivity.other
    ) as BufferSensitivity;

    const maxBitratePlayer = isVod ? maxBitrateVod : maxBitrate;

    return {
      autostart: true,
      drm: {
        servers: {
          'com.widevine.alpha': widevineUrl,
        },
        advanced: {
          'com.widevine.alpha': {
            serverCertificateUrl:
              import.meta.env.VITE_REACT_APP_USE_PPD_DRM_SERVER === 'true'
                ? config.player.drmPreProdServerCertificateUrl
                : config.player.drmProdServerCertificateUrl,
          },
        },
      },
      dashConfiguration: {
        streaming: {
          abr: { maxBitrate: { audio: -1, video: maxBitratePlayer } },
        },
      },
      subtitles: {
        enableSMPTE: true,
      },
      abr: {
        enabled: true,
        bufferSensitivity,
      },
      streaming: {
        jumpLargeGaps: true,
      },
      restrictions: {
        maxBandwidth: maxBitratePlayer,
        minHeight: 0,
        maxHeight: 0,
        maxPixels: 0,
        maxWidth: 0,
        minWidth: 0,
        minPixels: 0,
      },
    };
  }, [config, casToken, isLiveStream, isVod]);

  const initPlayer = useCallback(() => {
    if (player) {
      return;
    }
    const {
      player: { licenseKey },
    } = config;

    logger.debug('Create player instance');

    const playerInstance = createPlayer('video-player');
    playerInstance.license = licenseKey;
    playerInstance.on('error', errorHandler);
    setPlayer(playerInstance);
    logger.debug(`Player version: ${playerInstance.version}`);
  }, [player, config, logger, errorHandler]);

  const destroyPlayer = useCallback(() => {
    if (player) {
      logger.debug('Destroy player instance');
      player.off('error', errorHandler);

      // noinspection JSIgnoredPromiseFromCall
      player.destroy();
    }
  }, [player, errorHandler, logger]);

  const startPlayback = useCallback(
    (currentTime?: TimeInSeconds) => {
      if (!player) {
        return;
      }
      setPlayerOverlay(PlayerOverlayContent.Default);
      const loadAt = currentTime || 0;
      player.reset().then(() => {
        logger.debug('Configure player', playerConfiguration);
        player.configure(playerConfiguration);

        logger.debug('Loading media url', { mediaUrl });
        player
          .load({ url: mediaUrl, startTime: loadAt })
          .then(() => {
            const updatePictureInPictureOnContentChange = () => {
              const isPictureInPictureOpen = document.pictureInPictureElement;
              if (isPictureInPictureOpen) {
                player && player.videoElement.requestPictureInPicture();
              }
            };

            player.audioTrack = getInitialAutoTrack(
              player.audioTracks,
              LanguagesAutoTrackOptions.AUDIO_LANGUAGE,
            );
            player.textTrack = getInitialAutoTrack(
              player.textTracks,
              LanguagesAutoTrackOptions.SUBTITLES_LANGUAGE,
            );
            player.videoElement.loop = loop;
            player.currentTime = loadAt;
            updatePictureInPictureOnContentChange();
            // noinspection JSIgnoredPromiseFromCall
            player.play();
          })
          .finally(() => setIsLoaderVisible(false));
      });

      player.on('error', (err) =>
        errorHandler(err, () => {
          refetchToken && refetchToken();
          startPlayback();
        }),
      );
    },
    [
      player,
      setPlayerOverlay,
      logger,
      mediaUrl,
      isUnsupportedBrowser,
      playerConfiguration,
      setIsLoaderVisible,
      getInitialAutoTrack,
      loop,
      errorHandler,
      refetchToken,
    ],
  );

  useEffect(() => {
    if (playerErrorMessage.message && !playerErrorMessage.button) {
      destroyPlayer();
    }
  }, [destroyPlayer, playerErrorMessage]);

  useEffect(() => {
    initPlayer();

    return () => {
      destroyPlayer();
    };
  }, [initPlayer, destroyPlayer]);

  useEffect(
    () => {
      startPlayback(startPosition);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [startPlayback], // do not reset playback when startPosition change
  );

  return (
    <div id='player-wrapper' ref={containerRef}>
      <S.PlayerContainer data-testid='player-container' mode={mode}>
        <video id='video-player' />
      </S.PlayerContainer>
      <PlayerInstanceProvider player={player} containerRef={containerRef}>
        <PlayerSettingsProvider>
          <PlayerTimerProvider>
            <PlayerTimeLimitProvider>
              <PlayerStateProvider>
                <PlayerKeyActionsProvider>{children}</PlayerKeyActionsProvider>
              </PlayerStateProvider>
            </PlayerTimeLimitProvider>
          </PlayerTimerProvider>
        </PlayerSettingsProvider>
      </PlayerInstanceProvider>
    </div>
  );
};
