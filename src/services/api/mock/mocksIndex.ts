import { householdTveServiceMockHandlers } from 'services/api/newApi/core/household/mocks/tveService.mock';
import { householdMockHandlers } from 'services/api/newApi/core/household/mocks/household.mock';
import { searchDataMockHandlers } from 'services/api/oldApi/search/mocks/searchData.mock';
import { epgMockHandlers } from 'services/api/newApi/core/tvguide/mocks/epg.mock';
import { vodAssetMockHandlers } from 'services/api/oldApi/vod/mocks/vodAsset.mock';
import { vodPurchasesAssetMockHandlers } from 'services/api/oldApi/vod/mocks/vodPurchasesAsset.mock';
import { channelsAllMockHandlers } from 'services/api/newApi/live/channels/mocks/channelsAll.mock';
import { regionalListMockHandlers } from 'services/api/newApi/live/channels/mocks/regionalTvList.mock';
import { getDevicesMaxMockHandlers } from 'services/api/newApi/core/device/mocks/devicesHandlers.mock';

import { epgExtendedMockHandlers } from '../newApi/core/tvguide/mocks/epgExtended.mock';
import { appConfigsMockHandlers } from '../newApi/core/appConfigs/mocks/appConfigs.mock';

export {
  searchDataMockHandlers,
  epgMockHandlers,
  appConfigsMockHandlers,
  epgExtendedMockHandlers,
  vodAssetMockHandlers,
  vodPurchasesAssetMockHandlers,
  channelsAllMockHandlers,
  regionalListMockHandlers,
  getDevicesMaxMockHandlers,
  householdMockHandlers,
  householdTveServiceMockHandlers,
};
