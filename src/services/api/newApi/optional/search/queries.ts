import { useQuery } from 'react-query';

import { Channel } from 'services/api/newApi/live/channels';

import { ENDPOINTS } from './endpoints';
import { searchKeys } from './keys';
import { SearchRequest, SearchResponse, SearchResult } from './types';
import { addFullImagePaths } from './helpers';

import { newApiClient } from '../../client';

export const useSearchQuery = (
  params: SearchRequest,
  availableChannels: Channel[] = [],
) => {
  const { phrase } = params;

  return useQuery(
    searchKeys.result(params),
    async ({ signal }) => {
      const response = await newApiClient.get<SearchRequest, SearchResponse>(
        ENDPOINTS.GET_SEARCH_DATA,
        params,
        signal,
      );

      const resultsWithFullImagePaths = addFullImagePaths(
        response.data.results,
        response.data.imagePaths,
        availableChannels,
      );

      const filteredResults = resultsWithFullImagePaths.filter(
        (result: SearchResult) => {
          if (result.type === 'channel') {
            return availableChannels.some(
              (channel) => channel.channelExtId === result.extId,
            );
          }

          if (
            ['epgProgram', 'epgCommonPrograms', 'epgSeries'].includes(
              result.type,
            )
          ) {
            const channelIds: string[] = [];

            if (result.epgNearestCommonNamePrograms) {
              channelIds.push(
                ...result.epgNearestCommonNamePrograms.map(
                  (program) => program.channelExtId,
                ),
              );
            }

            if (result.epgSeriesNearestEpisodes) {
              channelIds.push(
                ...result.epgSeriesNearestEpisodes.map(
                  (episode) => episode.channelExtId,
                ),
              );
            }

            return channelIds.some((channelId) =>
              availableChannels.some(
                (channel) => channel.channelExtId === channelId,
              ),
            );
          }

          return true;
        },
      );

      return filteredResults;
    },
    {
      enabled: Boolean(phrase && phrase.length > 0),
    },
  );
};
