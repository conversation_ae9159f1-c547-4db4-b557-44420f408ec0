import { Channel } from 'services/api/newApi/live/channels';

import { SearchResponse, SearchResult } from './types';

export const addFullImagePaths = (
  results: SearchResult[],
  imagePaths: SearchResponse['imagePaths'],
  availableChannels: Channel[] = [],
): SearchResult[] => {
  return results.map((result) => {
    let fullImagePath = result.image;

    switch (result.type) {
      case 'vod':
        if (result.image) {
          if (imagePaths.vod?.standard) {
            fullImagePath = imagePaths.vod.standard + result.image;
          } else if (imagePaths.vod?.small) {
            fullImagePath = imagePaths.vod.small + result.image;
          }
        }
        break;

      case 'person':
        if (result.image) {
          if (imagePaths.person?.standard) {
            fullImagePath = imagePaths.person.standard + result.image;
          } else if (imagePaths.person?.small) {
            fullImagePath = imagePaths.person.small + result.image;
          }
        }

        if (!result.image && result.personInVodMovies?.[0]?.movieImage) {
          const movieImage = result.personInVodMovies[0].movieImage;
          if (imagePaths.vod?.standard) {
            fullImagePath = imagePaths.vod.standard + movieImage;
          } else if (imagePaths.vod?.small) {
            fullImagePath = imagePaths.vod.small + movieImage;
          }
        }

        if (!fullImagePath && result.personInEpgMovies?.[0]?.movieImage) {
          const movieImage = result.personInEpgMovies[0].movieImage;
          if (imagePaths.epg?.standard) {
            fullImagePath = imagePaths.epg.standard + movieImage;
          } else if (imagePaths.epg?.small) {
            fullImagePath = imagePaths.epg.small + movieImage;
          }
        }
        break;

      case 'channel':
        if (result.image) {
          if (imagePaths.channel?.standard) {
            fullImagePath = imagePaths.channel.standard + result.image;
          } else if (imagePaths.channel?.small) {
            fullImagePath = imagePaths.channel.small + result.image;
          }
        }
        break;

      case 'epgProgram':
      case 'epgCommonPrograms':
      case 'epgSeries':
        if (result.image) {
          if (imagePaths.epg?.standard) {
            fullImagePath = imagePaths.epg.standard + result.image;
          } else if (imagePaths.epg?.small) {
            fullImagePath = imagePaths.epg.small + result.image;
          }
        }

        if (!result.image && result.epgNearestCommonNamePrograms?.[0]?.image) {
          const programImage = result.epgNearestCommonNamePrograms[0].image;
          if (imagePaths.epg?.standard) {
            fullImagePath = imagePaths.epg.standard + programImage;
          } else if (imagePaths.epg?.small) {
            fullImagePath = imagePaths.epg.small + programImage;
          }
        }

        if (
          !fullImagePath &&
          result.epgSeriesNearestEpisodes?.[0]?.episodeImage
        ) {
          const episodeImage = result.epgSeriesNearestEpisodes[0].episodeImage;
          if (imagePaths.epg?.standard) {
            fullImagePath = imagePaths.epg.standard + episodeImage;
          } else if (imagePaths.epg?.small) {
            fullImagePath = imagePaths.epg.small + episodeImage;
          }
        }

        if (!fullImagePath) {
          let channelExtId: string | undefined;

          if (result.epgNearestCommonNamePrograms?.[0]?.channelExtId) {
            channelExtId = result.epgNearestCommonNamePrograms[0].channelExtId;
          } else if (result.epgSeriesNearestEpisodes?.[0]?.channelExtId) {
            channelExtId = result.epgSeriesNearestEpisodes[0].channelExtId;
          }

          if (channelExtId) {
            const channel = availableChannels.find(
              (ch) => ch.channelExtId === channelExtId,
            );
            if (channel?.logoUrl) {
              fullImagePath = channel.logoUrl;
            }
          }
        }
        break;

      default:
        break;
    }

    return {
      ...result,
      image: fullImagePath,
    };
  });
};
