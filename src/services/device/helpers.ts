import { globalConfig } from 'services/config/config';

export const getBrowserType = (): string => {
  if (navigator.userAgent.indexOf('Edge') !== -1) {
    return 'EDGE';
  }
  if (
    navigator.userAgent.indexOf('Opera') !== -1 ||
    navigator.userAgent.indexOf('OPR') !== -1
  ) {
    return 'OPERA';
  }
  if (navigator.userAgent.indexOf('Chrome') !== -1) {
    return 'CHROME';
  }
  // Safari check must be after Chrome because Chrome also contains "Safari" in user agent
  if (
    navigator.userAgent.indexOf('Safari') !== -1 &&
    navigator.userAgent.indexOf('Chrome') === -1
  ) {
    return 'SAFARI';
  }
  if (navigator.userAgent.indexOf('PaleMoon') !== -1) {
    return 'PaleMoon';
  }
  if (navigator.userAgent.indexOf('Firefox') !== -1) {
    return 'FIREFOX';
  }
  if (
    navigator.userAgent.indexOf('MSIE 11') !== -1 ||
    navigator.userAgent.indexOf('Trident/7') !== -1
  ) {
    return 'IE_11';
  }
  if (navigator.userAgent.indexOf('MSIE') !== -1) {
    return 'OLD_IE';
  }
  return 'UNKNOWN';
};

export const generateTerminalSerialNumber = (
  browserType: string,
  serialNumberType: string,
  householdExtId: string,
) => {
  return `${browserType}_${serialNumberType}${householdExtId}`;
};

export const checkIsUnsupportedBrowser = (): boolean =>
  globalConfig.unsupportedBrowsers.includes(getBrowserType());
