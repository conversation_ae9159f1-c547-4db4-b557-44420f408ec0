import { useIntl } from 'react-intl';
import { createPortal } from 'react-dom';

import { IconWarning } from 'components/Icons';

import { messages } from './messages';
import * as S from './styles';

interface UnsupportedBrowserModalProps {
  isOpen: boolean;
}

export const UnsupportedBrowserModal = ({
  isOpen,
}: UnsupportedBrowserModalProps) => {
  const { formatMessage } = useIntl();

  if (!isOpen) {
    return null;
  }

  return (
    <>
      {createPortal(
        <S.Overlay>
          <S.Container>
            <S.Content>
              <IconWarning />
              <S.StyledTitle $sizeLarge>
                {formatMessage(messages.unsupportedBrowserModalTitle)}
              </S.StyledTitle>
              <S.StyledText $sizeMedium>
                {formatMessage(messages.unsupportedBrowserModalInformation)}
              </S.StyledText>
            </S.Content>
          </S.Container>
        </S.Overlay>,
        document.getElementById('appShell') || document.body,
      )}
    </>
  );
};
