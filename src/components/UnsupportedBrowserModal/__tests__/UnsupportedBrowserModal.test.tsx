import { render, screen } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { ThemeProvider } from 'styled-components';

import { UnsupportedBrowserModal } from '../UnsupportedBrowserModal';

const mockTheme = {
  colors: {
    black: '#000000',
    white: '#ffffff',
    alabaster: '#f5f5f5',
    white80: 'rgba(255, 255, 255, 0.8)',
    white50: 'rgba(255, 255, 255, 0.5)',
    primary: '#ff6600',
    red: '#ff0000',
  },
  fontSizes: {
    xLarge: '3.6rem',
    large: '3rem',
    medium: '2.4rem',
    xMedium: '2.2rem',
    xxMedium: '2rem',
    small: '1.8rem',
    xSmall: '1.6rem',
    xxSmall: '1.4rem',
  },
  mixins: {
    baseTextColor: () => 'color: #f5f5f5;',
    baseTextSize: () => 'font-size: 2.4rem;',
    baseTextWrap: () => '',
  },
};

const messages = {
  'unsupportedBrowserModal.title': 'Twoja przeglądarka nie jest obsługiwana.',
  'unsupportedBrowserModal.information':
    'Zalecamy skorzystanie z najnowszej wersji jednej z następujących przeglądarek:\nGoogle Chrome, Mozilla Firefox, Microsoft Edge, Opera.',
};

const renderWithProviders = (component: JSX.Element) => {
  return render(
    <IntlProvider locale='pl' messages={messages}>
      <ThemeProvider theme={mockTheme}>{component}</ThemeProvider>
    </IntlProvider>,
  );
};

describe('UnsupportedBrowserModal', () => {
  it('should not render when isOpen is false', () => {
    renderWithProviders(<UnsupportedBrowserModal isOpen={false} />);

    expect(
      screen.queryByText('Twoja przeglądarka nie jest obsługiwana.'),
    ).not.toBeInTheDocument();
  });

  it('should render when isOpen is true', () => {
    renderWithProviders(<UnsupportedBrowserModal isOpen={true} />);

    expect(
      screen.getByText('Twoja przeglądarka nie jest obsługiwana.'),
    ).toBeInTheDocument();
    expect(
      screen.getByText(/Zalecamy skorzystanie z najnowszej wersji/),
    ).toBeInTheDocument();
  });
});
