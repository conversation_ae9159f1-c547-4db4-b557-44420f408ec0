import styled from 'styled-components';

import { Text } from 'components/Typography';
import { RenderLayer } from 'theme';

export const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${({ theme }) => theme.colors.black};
  display: flex;
  align-items: center;
  justify-content: center;
  ${RenderLayer('modal')};
`;

export const Container = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
`;

export const Content = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  row-gap: 1.6rem;
  text-align: center;
  max-width: 50rem;
  padding: 2rem;

  svg {
    margin-bottom: 1.6rem;
  }
`;

export const StyledTitle = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-weight: bold;
`;

export const StyledText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  white-space: pre-line;
  text-align: center;
`;
